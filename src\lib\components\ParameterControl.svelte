<script lang="ts">
  // Props
  export let label: string;
  export let value: number;
  export let min: number;
  export let max: number;
  export let step: number = 0.1;
  export let onchange: (value: number) => void;

  // State
  let isOpen = false;
  let buttonElement: HTMLButtonElement;
  let sliderContainer: HTMLDivElement;

  // Toggle slider visibility
  const toggleSlider = () => {
    isOpen = !isOpen;
  };

  // Handle slider change
  const handleSliderChange = (event: Event) => {
    const newValue = parseFloat((event.target as HTMLInputElement).value);
    onchange(newValue);
  };

  // Handle click outside to close
  const handleClickOutside = (event: MouseEvent) => {
    if (isOpen && 
        buttonElement && 
        sliderContainer && 
        !buttonElement.contains(event.target as Node) && 
        !sliderContainer.contains(event.target as Node)) {
      isOpen = false;
    }
  };

  // Handle escape key
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen) {
      isOpen = false;
    }
  };

  // Format value for display
  const formatValue = (val: number): string => {
    return val.toFixed(2);
  };
</script>

<svelte:window on:click={handleClickOutside} on:keydown={handleKeydown} />

<div class="parameter-control">
  <button
    bind:this={buttonElement}
    class="parameter-button"
    class:active={isOpen}
    on:click={toggleSlider}
    type="button"
  >
    <span class="parameter-label">{label}:</span>
    <span class="parameter-value">{formatValue(value)}</span>
    <span class="parameter-arrow" class:rotated={isOpen}>▼</span>
  </button>

  {#if isOpen}
    <div bind:this={sliderContainer} class="slider-container">
      <input
        type="range"
        class="parameter-slider"
        {min}
        {max}
        {step}
        {value}
        on:input={handleSliderChange}
      />
      <div class="slider-labels">
        <span>{min}</span>
        <span>{max}</span>
      </div>
    </div>
  {/if}
</div>

<style>
  .parameter-control {
    position: relative;
    width: 100%;
  }

  .parameter-button {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--color-border);
    border-radius: 3px;
    background-color: var(--color-input-bg);
    color: var(--color-text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }

  .parameter-button:hover {
    border-color: var(--color-accent);
    background-color: var(--color-toggle-hover);
  }

  .parameter-button:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.25);
  }

  .parameter-button.active {
    border-color: var(--color-accent);
    background-color: var(--color-toggle-hover);
  }

  .parameter-label {
    font-weight: 500;
    flex-shrink: 0;
  }

  .parameter-value {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: var(--color-accent);
    flex: 1;
    text-align: right;
  }

  .parameter-arrow {
    font-size: 0.8rem;
    transition: transform 0.2s ease;
    flex-shrink: 0;
  }

  .parameter-arrow.rotated {
    transform: rotate(180deg);
  }

  .slider-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: var(--color-input-bg);
    border: 1px solid var(--color-accent);
    border-top: none;
    border-radius: 0 0 3px 3px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .parameter-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--color-border);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
  }

  .parameter-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--color-accent);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .parameter-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--color-accent);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 0.8rem;
    color: var(--color-text-primary);
    opacity: 0.7;
  }
</style>
