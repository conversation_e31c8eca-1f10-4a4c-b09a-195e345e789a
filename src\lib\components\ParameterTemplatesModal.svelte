<script lang="ts">
  // Props
  export let isOpen: boolean;
  export let close: () => void;
  export let onTemplateSelect: (temperature: number, topP: number) => void;

  // Template definitions
  const templates = [
    {
      name: "Code Generation",
      temperature: 0.2,
      topP: 0.1,
      description: "Generates code that adheres to established patterns and conventions. Output is more deterministic and focused. Useful for generating syntactically correct code."
    },
    {
      name: "Creative Writing",
      temperature: 0.7,
      topP: 0.8,
      description: "Generates creative and diverse text for storytelling. Output is more exploratory and less constrained by patterns."
    },
    {
      name: "Chatbot Responses",
      temperature: 0.5,
      topP: 0.5,
      description: "Generates conversational responses that balance coherence and diversity. Output is more natural and engaging."
    },
    {
      name: "Code Comment Generation",
      temperature: 0.3,
      topP: 0.2,
      description: "Generates code comments that are more likely to be concise and relevant. Output is more deterministic and adheres to conventions."
    },
    {
      name: "Data Analysis Scripting",
      temperature: 0.2,
      topP: 0.1,
      description: "Generates data analysis scripts that are more likely to be correct and efficient. Output is more deterministic and focused."
    },
    {
      name: "Exploratory Code Writing",
      temperature: 0.6,
      topP: 0.7,
      description: "Generates code that explores alternative solutions and creative approaches. Output is less constrained by established patterns."
    }
  ];

  // Handle template selection
  const selectTemplate = (template: typeof templates[0]) => {
    onTemplateSelect(template.temperature, template.topP);
    close();
  };

  // Handle backdrop click
  const handleBackdropClick = (event: MouseEvent) => {
    if (event.target === event.currentTarget) {
      close();
    }
  };

  // Handle escape key
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      close();
    }
  };
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
  <div
    class="modal-backdrop"
    on:click={handleBackdropClick}
    on:keydown={handleKeydown}
    role="presentation"
  >
    <div
      class="modal-content"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      tabindex="-1"
    >
      <div class="modal-header">
        <h2 id="modal-title">Parameter Templates</h2>
        <button class="close-button" on:click={close} type="button">×</button>
      </div>
      
      <div class="modal-body">
        <p class="modal-description">
          Select a predefined template to quickly configure temperature and top_p values for different use cases.
        </p>
        
        <div class="templates-grid">
          {#each templates as template}
            <button 
              class="template-card" 
              on:click={() => selectTemplate(template)}
              type="button"
            >
              <div class="template-header">
                <h3 class="template-name">{template.name}</h3>
                <div class="template-values">
                  <span class="template-value">T: {template.temperature}</span>
                  <span class="template-value">P: {template.topP}</span>
                </div>
              </div>
              <p class="template-description">{template.description}</p>
            </button>
          {/each}
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
    box-sizing: border-box;
  }

  .modal-content {
    background-color: var(--color-input-bg);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--color-border);
    background-color: var(--color-sidebar-bg);
  }

  .modal-header h2 {
    margin: 0;
    color: var(--color-text-primary);
    font-size: 1.25rem;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--color-text-primary);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .close-button:hover {
    background-color: var(--color-toggle-hover);
  }

  .modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
  }

  .modal-description {
    margin: 0 0 20px 0;
    color: var(--color-text-primary);
    opacity: 0.8;
    line-height: 1.5;
  }

  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 16px;
  }

  .template-card {
    background-color: var(--color-input-bg);
    border: 1px solid var(--color-border);
    border-radius: 6px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
  }

  .template-card:hover {
    border-color: var(--color-accent);
    background-color: var(--color-toggle-hover);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
  }

  .template-name {
    margin: 0;
    color: var(--color-text-primary);
    font-size: 1rem;
    font-weight: 600;
  }

  .template-values {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  }

  .template-value {
    background-color: var(--color-accent);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    font-family: 'Courier New', monospace;
  }

  .template-description {
    margin: 0;
    color: var(--color-text-primary);
    opacity: 0.8;
    font-size: 0.9rem;
    line-height: 1.4;
  }
</style>
